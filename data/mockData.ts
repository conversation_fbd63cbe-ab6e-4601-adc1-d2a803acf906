import { DiagnosisResult, Plant, PlantInCollection, RecentScan } from '../types/plant';

export const mockPlants: Plant[] = [
  {
    id: '1',
    name: '<PERSON><PERSON> Deliciosa',
    scientificName: '<PERSON>a deliciosa',
    confidence: 95,
    family: 'Araceae',
    commonNames: ['Swiss Cheese Plant', 'Split-leaf Philodendron'],
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
    careRequirements: {
      light: 'Bright, indirect light',
      water: 'Water when top inch of soil is dry',
      temperature: '65-80°F (18-27°C)',
    },
  },
  {
    id: '2',
    name: 'Snake Plant',
    scientificName: 'Sansevieria trifasciata',
    confidence: 92,
    family: 'Asparagaceae',
    commonNames: ['Mother-in-Law\'s Tongue', 'Viper\'s Bowstring Hemp'],
    image: 'https://images.unsplash.com/photo-1593691509543-c55fb32d8de5?w=400',
    careRequirements: {
      light: 'Low to bright, indirect light',
      water: 'Water every 2-3 weeks',
      temperature: '60-80°F (15-27°C)',
    },
  },
  {
    id: '3',
    name: 'Fiddle Leaf Fig',
    scientificName: 'Ficus lyrata',
    confidence: 88,
    family: 'Moraceae',
    commonNames: ['Banjo Fig'],
    image: 'https://images.unsplash.com/photo-1586093248292-4e6636c04c3d?w=400',
    careRequirements: {
      light: 'Bright, indirect light',
      water: 'Water when top 2 inches of soil are dry',
      temperature: '65-75°F (18-24°C)',
    },
  },
];

export const mockPlantsInCollection: PlantInCollection[] = [
  {
    id: '1',
    name: 'Monstera Deliciosa',
    scientificName: 'Monstera deliciosa',
    nickname: 'Monty',
    health: 85,
    lastWateredDate: new Date('2024-07-25'),
    status: 'healthy',
    wateringFrequencyDays: 7,
    notes: 'Growing new leaves regularly',
    category: 'indoor',
    addedDate: new Date('2024-07-20'),
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
    careRequirements: {
      light: 'Bright, indirect light',
      water: 'Water when top inch of soil is dry',
      temperature: '65-80°F (18-27°C)',
    },
  },
  {
    id: '2',
    name: 'Snake Plant',
    scientificName: 'Sansevieria trifasciata',
    nickname: 'Sammy',
    health: 95,
    lastWateredDate: new Date('2024-07-20'),
    status: 'healthy',
    wateringFrequencyDays: 14,
    notes: 'Very low maintenance',
    category: 'indoor',
    addedDate: new Date('2024-07-18'),
    image: 'https://images.unsplash.com/photo-1593691509543-c55fb32d8de5?w=400',
    careRequirements: {
      light: 'Low to bright, indirect light',
      water: 'Water every 2-3 weeks',
      temperature: '60-80°F (15-27°C)',
    },
  },
  {
    id: '3',
    name: 'Peace Lily',
    scientificName: 'Spathiphyllum wallisii',
    nickname: 'Lily',
    health: 70,
    lastWateredDate: new Date('2024-07-26'),
    status: 'needs-attention',
    wateringFrequencyDays: 5,
    notes: 'Leaves drooping slightly',
    category: 'flowering',
    addedDate: new Date('2024-07-22'),
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
    careRequirements: {
      light: 'Medium to bright, indirect light',
      water: 'Keep soil consistently moist',
      temperature: '65-80°F (18-27°C)',
    },
  },
];

export const mockRecentScans: RecentScan[] = [
  {
    id: '1',
    name: 'Monstera Deliciosa',
    confidence: 95,
    date: new Date('2024-07-27'),
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
    result: {
      name: 'Monstera Deliciosa',
      scientificName: 'Monstera deliciosa',
      commonName: 'Swiss Cheese Plant',
      confidence: 95,
    },
  },
  {
    id: '2',
    name: 'Snake Plant',
    confidence: 92,
    date: new Date('2024-07-26'),
    image: 'https://images.unsplash.com/photo-1593691509543-c55fb32d8de5?w=400',
    result: {
      name: 'Snake Plant',
      scientificName: 'Sansevieria trifasciata',
      commonName: 'Mother-in-Law\'s Tongue',
      confidence: 92,
    },
  },
  {
    id: '3',
    name: 'Pothos',
    confidence: 89,
    date: new Date('2024-07-25'),
    image: 'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400',
    result: {
      name: 'Pothos',
      scientificName: 'Epipremnum aureum',
      commonName: 'Golden Pothos',
      confidence: 89,
    },
  },
];

export const mockDiagnosisResults: DiagnosisResult[] = [
  {
    issue: 'Overwatering',
    severity: 'Medium',
    advice: 'Reduce watering frequency and ensure proper drainage. Allow soil to dry between waterings.',
    symptoms: ['Yellow leaves', 'Soft stems', 'Musty smell'],
    remarks: 'Common issue with indoor plants during winter months.',
    confidence: 85,
    description: 'Overwatering is one of the most common causes of plant problems, leading to root rot and yellowing leaves.',
    causes: ['Too frequent watering', 'Poor drainage', 'Heavy soil'],
    treatments: [
      {
        title: 'Reduce Watering',
        description: 'Allow soil to dry between waterings',
        steps: ['Check soil moisture before watering', 'Water only when top inch is dry', 'Ensure proper drainage'],
      },
      {
        title: 'Improve Drainage',
        description: 'Enhance soil drainage to prevent water logging',
        steps: ['Add perlite to soil mix', 'Use pots with drainage holes', 'Consider repotting'],
      },
    ],
    prevention: ['Check soil moisture regularly', 'Ensure proper drainage', 'Avoid watering on schedule'],
  },
  {
    issue: 'Nutrient Deficiency',
    severity: 'Low',
    advice: 'Apply balanced liquid fertilizer every 2-3 weeks during growing season.',
    symptoms: ['Pale leaves', 'Slow growth', 'Small new leaves'],
    remarks: 'Easily correctable with proper fertilization.',
    confidence: 78,
    description: 'Plants require essential nutrients for healthy growth and development.',
    causes: ['Lack of fertilization', 'Poor soil quality', 'Nutrient leaching'],
    treatments: [
      {
        title: 'Apply Fertilizer',
        description: 'Use balanced liquid fertilizer during growing season',
        steps: ['Dilute fertilizer to half strength', 'Apply every 2-3 weeks', 'Water before fertilizing'],
      },
      {
        title: 'Soil Improvement',
        description: 'Enhance soil quality with organic matter',
        steps: ['Add compost to soil', 'Use quality potting mix', 'Consider slow-release fertilizer'],
      },
    ],
    prevention: ['Regular fertilization schedule', 'Use quality potting soil', 'Monitor plant growth'],
  },
  {
    issue: 'Spider Mites',
    severity: 'High',
    advice: 'Isolate plant immediately. Spray with neem oil or insecticidal soap. Increase humidity.',
    symptoms: ['Fine webbing', 'Stippled leaves', 'Tiny moving dots'],
    remarks: 'Requires immediate attention to prevent spread.',
    confidence: 92,
    description: 'Spider mites are tiny pests that can quickly damage plants and spread to other plants.',
    causes: ['Low humidity', 'Warm dry conditions', 'Stressed plants'],
    treatments: [
      {
        title: 'Neem Oil Treatment',
        description: 'Apply neem oil to affected areas',
        steps: ['Spray in evening to avoid leaf burn', 'Cover all surfaces including undersides', 'Repeat every 3-5 days'],
      },
      {
        title: 'Environmental Control',
        description: 'Modify environment to prevent re-infestation',
        steps: ['Increase humidity around plant', 'Improve air circulation', 'Isolate from other plants'],
      },
    ],
    prevention: ['Maintain proper humidity', 'Regular plant inspection', 'Quarantine new plants'],
  },
];

// Mock function to simulate plant identification
export const identifyPlant = async (imageUri: string): Promise<Plant> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Return random plant from mock data
  const randomIndex = Math.floor(Math.random() * mockPlants.length);
  return {
    ...mockPlants[randomIndex],
    image: imageUri, // Use the captured image
  };
};

// Mock function to simulate plant diagnosis
export const diagnosePlant = async (imageUri: string): Promise<DiagnosisResult> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Return random diagnosis from mock data
  const randomIndex = Math.floor(Math.random() * mockDiagnosisResults.length);
  return {
    ...mockDiagnosisResults[randomIndex],
    photo: imageUri, // Use the captured image
  };
};
